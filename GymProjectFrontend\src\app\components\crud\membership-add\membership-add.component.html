<div class="container-fluid mt-4 fade-in">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Üyelik bilgileri yükleniyor">
  </app-loading-spinner>

  <div class="row">
    <div class="col-md-12" [class.content-blur]="isLoading">
      <div class="modern-card">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-id-card me-2"></i>
            Üyelik Paneli
          </h5>
        </div>

        <!-- Üyelik türü yoksa genel uyarı mesajı -->
        <div class="alert alert-warning m-3" *ngIf="membershipTypes && membershipTypes.length === 0">
          <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle me-3 fa-2x"></i>
            <div>
              <h5 class="mb-1">Üyelik Türü Bulunamadı</h5>
              <p class="mb-2">Üyelik eklemek için önce üyelik türü tanımlamanız gerekmektedir.</p>
              <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" (click)="navigateToMembershipTypeAdd()">
                <i class="fas fa-plus-circle modern-btn-icon"></i> Üyelik Türü Ekle
              </button>
            </div>
          </div>
        </div>

        <div class="modern-card-body">
          <!-- Form Progress Indicator -->
          <div class="progress mb-4" style="height: 6px;">
            <div
              class="progress-bar bg-primary"
              [style.width]="getFormProgress() + '%'"
              role="progressbar"
              [attr.aria-valuenow]="getFormProgress()"
              aria-valuemin="0"
              aria-valuemax="100">
            </div>
          </div>

          <form [formGroup]="membershipAddForm" class="fade-in">
            <!-- Üye Bilgileri Section -->
            <div class="form-section mb-4">
              <h6 class="section-title">
                <i class="fas fa-user me-2"></i>
                Üye Bilgileri
              </h6>

              <div class="row">
                <div class="col-md-6 mb-3">
                  <div class="modern-form-group">
                    <label for="memberID" class="modern-form-label required">Ad - Telefon</label>
                    <div class="input-group">
                      <span class="input-group-text">
                        <i class="fas fa-user"></i>
                      </span>
                      <input
                        type="text"
                        id="memberID"
                        formControlName="memberID"
                        class="modern-form-control"
                        placeholder="Üye adı veya telefon numarası"
                        [matAutocomplete]="autoMember"
                        required
                      />
                      <mat-autocomplete #autoMember="matAutocomplete" [displayWith]="displayMember">
                        <mat-option *ngFor="let member of filteredMembers | async" [value]="member">
                          {{ member.name }} - {{ member.phoneNumber }}
                        </mat-option>
                      </mat-autocomplete>
                    </div>
                  </div>
                </div>

                <div class="col-md-6 mb-3">
                  <div class="modern-form-group">
                    <label for="membershipTypeID" class="modern-form-label required">Üyelik Türü</label>
                    <div class="input-group">
                      <span class="input-group-text" [ngClass]="{'text-danger': membershipAddForm.get('membershipTypeID')?.invalid && membershipAddForm.get('membershipTypeID')?.touched}">
                        <i class="fas fa-id-card"></i>
                      </span>
                      <input
                        type="text"
                        id="membershipTypeID"
                        formControlName="membershipTypeID"
                        class="modern-form-control"
                        [class.is-invalid]="membershipAddForm.get('membershipTypeID')?.invalid && membershipAddForm.get('membershipTypeID')?.touched"
                        [matAutocomplete]="autoMembershipType"
                        (click)="showBranchList = true"
                        placeholder="Üyelik türü yazın veya seçin..."
                        required
                      />
                      <mat-autocomplete #autoMembershipType="matAutocomplete" [displayWith]="displayMembershipType">
                        <div *ngIf="showBranchList">
                          <mat-option
                            *ngFor="let membershipType of filteredMembershipTypes | async"
                            [value]="membershipType.branch + ' - ' + membershipType.typeName"
                          >
                            {{ membershipType.branch }} - {{ membershipType.typeName }}
                          </mat-option>

                          <!-- Üyelik türü yoksa bilgilendirme mesajı ve yönlendirme butonu -->
                          <mat-option *ngIf="membershipTypes && membershipTypes.length === 0" [disabled]="true" class="no-membership-type-option">
                            <div class="alert alert-warning mb-0">
                              <i class="fas fa-exclamation-triangle me-2"></i>
                              Henüz üyelik türü tanımlanmamış. Önce üyelik türü eklemelisiniz.
                            </div>
                            <button type="button" class="modern-btn modern-btn-primary modern-btn-sm mt-2" (click)="navigateToMembershipTypeAdd()">
                              <i class="fas fa-plus-circle modern-btn-icon"></i> Üyelik Türü Ekle
                            </button>
                          </mat-option>

                          <!-- Filtreleme sonucu bulunamadığında -->
                          <mat-option *ngIf="(filteredMembershipTypes | async)?.length === 0 && membershipTypes.length > 0" [disabled]="true">
                            <div class="text-muted text-center py-2">
                              <i class="fas fa-search me-2"></i>
                              Aradığınız kriterlere uygun üyelik türü bulunamadı
                            </div>
                          </mat-option>
                        </div>
                      </mat-autocomplete>
                    </div>

                    <!-- Hata mesajları -->
                    <div class="invalid-feedback" *ngIf="membershipAddForm.get('membershipTypeID')?.invalid && membershipAddForm.get('membershipTypeID')?.touched">
                      <div *ngIf="membershipAddForm.get('membershipTypeID')?.errors?.['required']">
                        <i class="fas fa-exclamation-circle me-1"></i>
                        Üyelik türü seçimi zorunludur
                      </div>
                      <div *ngIf="membershipAddForm.get('membershipTypeID')?.errors?.['invalidSelection']">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Lütfen listeden geçerli bir üyelik türü seçiniz
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Üyelik Detayları Section -->
            <div class="form-section mb-4">
              <h6 class="section-title">
                <i class="fas fa-calendar-alt me-2"></i>
                Üyelik Detayları
              </h6>

              <div class="row">
                <div class="col-md-6 mb-3">
                  <div class="modern-form-group">
                    <label class="modern-form-label required">Başlangıç Tarihi</label>
                    <div class="input-group">
                      <span class="input-group-text">
                        <i class="fas fa-calendar"></i>
                      </span>
                      <input
                        type="date"
                        class="modern-form-control"
                        formControlName="startDate"
                        required
                      >
                    </div>
                    <div class="last-membership-info" *ngIf="lastMembershipInfo">
                      <small>{{ lastMembershipInfo }}</small>
                    </div>
                  </div>
                </div>

                <div class="col-md-6 mb-3">
                  <div class="modern-form-group">
                    <label for="day" class="modern-form-label">Gün Sayısı</label>
                    <div class="input-group">
                      <span class="input-group-text">
                        <i class="fas fa-clock"></i>
                      </span>
                      <input
                        type="number"
                        id="day"
                        formControlName="day"
                        class="modern-form-control"
                        placeholder="Gün sayısı"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Ödeme Bilgileri Section -->
            <div class="form-section mb-4">
              <h6 class="section-title">
                <i class="fas fa-credit-card me-2"></i>
                Ödeme Bilgileri
              </h6>

              <div class="row">
                <div class="col-md-6 mb-3">
                  <div class="modern-form-group">
                    <label for="price" class="modern-form-label">Ücret</label>
                    <div class="input-group">
                      <span class="input-group-text">
                        <i class="fas fa-money-bill"></i>
                      </span>
                      <input
                        type="number"
                        id="price"
                        formControlName="price"
                        class="modern-form-control"
                        placeholder="Ücret tutarı"
                      />
                    </div>
                  </div>
                </div>

                <div class="col-md-6 mb-3">
                  <div class="modern-form-group">
                    <label for="PaymentMethod" class="modern-form-label required">Ödeme Türü</label>
                    <div class="input-group">
                      <span class="input-group-text">
                        <i class="fas fa-money-bill-wave"></i>
                      </span>
                      <select
                        id="PaymentMethod"
                        formControlName="PaymentMethod"
                        class="modern-form-control"
                        required
                      >
                        <option value="">Seçiniz</option>
                        <option value="Nakit">Nakit</option>
                        <option value="Kredi Kartı">Kredi Kartı</option>
                        <option value="Havale - EFT">Havale - EFT</option>
                        <option value="Borç">Borç</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

        <div class="modern-card-footer">
          <div class="d-flex justify-content-end gap-3">
            <button
              class="modern-btn modern-btn-primary modern-btn-lg"
              (click)="add()"
              [disabled]="isSubmitting || (membershipTypes && membershipTypes.length === 0)">
              <i class="fas fa-plus-circle modern-btn-icon" *ngIf="!isSubmitting"></i>
              <i class="fas fa-spinner fa-spin modern-btn-icon" *ngIf="isSubmitting"></i>
              {{ isSubmitting ? 'Üyelik Ekleniyor...' : 'Üyelik Ekle' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>